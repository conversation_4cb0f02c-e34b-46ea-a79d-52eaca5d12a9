<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="query"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @pagination="query"
            >
                <template #rightNav>
                    <div class="right-nav-container">
                        <!-- TODO: 权限控制 -->
                        <el-button type="text" @click="addTemporaryProject" class="create-demand-button"
                            ><i class="el-icon-plus"></i> 新增临时项目
                        </el-button>
                        <el-button type="text" @click="addFormalProject" class="create-demand-button"
                            ><i class="el-icon-plus"></i> 新增正式项目
                        </el-button>
                        <el-button type="text" @click="downloadProjectInfo" class="create-demand-button"
                            ><i class="el-icon-download"></i> 下载项目信息
                        </el-button>
                    </div>
                </template>
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <!-- 只有正式项目可以编辑 -->
                    <!-- <el-button v-if="row.enableStatus === '正式'" type="text" size="small" @click="handleEdit(row)"
                        >编辑</el-button
                    > -->
                    <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
                </template>
            </project-list>
        </div>

        <!-- 新增临时项目弹窗 -->
        <TemporaryProjectDialog
            :visible.sync="temporaryProjectDialogVisible"
            @success="handleTemporaryProjectSuccess"
        />
        <!-- 查看临时项目详情弹窗 -->
        <TemporaryProjectDetailDialog :visible.sync="temporaryProjectDetailDialogVisible" />

        <!-- 新增正式项目弹窗 -->
        <FormalProjectDialog
            :type="formalProjectDialogType"
            :visible.sync="formalProjectDialogVisible"
            :projectId="currentProjectId"
            @success="handleFormalProjectSuccess"
        />
        <!-- 查看正式项目详情弹窗 -->
        <FormalProjectDetailDialog :visible.sync="formalProjectDetailDialogVisible" />
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import TemporaryProjectDialog from './components/TemporaryProjectDialog.vue';
import TemporaryProjectDetailDialog from './components/TemporaryProjectDetailDialog.vue';
import FormalProjectDialog from './components/FormalProjectDialog.vue';
import FormalProjectDetailDialog from './components/FormalProjectDetailDialog.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';

export default {
    name: 'ProjectManagementList',
    components: {
        ProjectList,
        TemporaryProjectDialog,
        TemporaryProjectDetailDialog,
        FormalProjectDialog,
        FormalProjectDetailDialog
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '进行中',
            // 导航栏配置
            navItems,
            // 查询参数
            params: { projectStatus: '进行中' },
            // 查询参数
            queryParams,
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 10,
            // 当前选中的项目ID
            currentProjectId: null,
            // 正式项目新增/编辑类型
            formalProjectDialogType: 'add',
            // 新增/编辑临时项目弹窗
            temporaryProjectDialogVisible: false,
            // 查看临时项目详情弹窗
            temporaryProjectDetailDialogVisible: false,
            // 新增/编辑正式项目弹窗
            formalProjectDialogVisible: false,
            // 查看正式项目详情弹窗
            formalProjectDetailDialogVisible: false
        };
    },
    created() {
        this.query();
    },
    methods: {
        handleFormalProjectSuccess() {
            this.query();
        },
        // 加载产品数据
        async query() {
            try {
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    ...this.params
                };

                // 日期处理
                // if (params.foundDate && params.foundDate.length > 0) {
                //     params.startDateString = params.foundDate[0];
                //     params.endDateString = params.foundDate[1];
                // }
                // if (params.closeDate && params.closeDate.length > 0) {
                //     params.closeStartDateString = params.closeDate[0];
                //     params.closeEndDateString = params.closeDate[1];
                // }

                const res = await this.$service.dms.project.getProjectList(params);

                if (res.code === '0000') {
                    this.productData = res.data?.list || [];
                    // 添加序号
                    if (res?.data?.startRow) {
                        this.productData = this.productData.map((item, index) => {
                            item.index = res.data.startRow + index;
                            return item;
                        });
                    }
                    this.total = res.data?.total || 0;
                } else {
                    this.$message.error(res.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.params = this.queryParams;
            this.query();
        },

        // 处理重置
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            this.query();
        },

        // 处理导航切换
        handleNavChange() {},

        // 编辑产品
        handleEdit(row) {
            this.formalProjectDialogVisible = true;
            this.formalProjectDialogType = 'edit';
            this.currentProjectId = row.projectId;
        },

        // 查看产品详情
        handleView(row) {
            this.currentProjectId = row.projectId;
            this.formalProjectDetailDialogVisible = true;
            return;
            if (row.enableStatus === '正式') {
                this.formalProjectDetailDialogVisible = true;
                return;
            }
            this.temporaryProjectDetailDialogVisible = true;
        },

        /**
         * 新增临时项目
         */
        addTemporaryProject() {
            this.temporaryProjectDialogVisible = true;
        },

        /**
         * 新增正式项目
         */
        addFormalProject() {
            this.formalProjectDialogVisible = true;
            this.formalProjectDialogType = 'add';
        },

        /**
         * 创建需求
         */
        downloadProjectInfo() {},

        /**
         * 临时项目创建成功回调
         */
        handleTemporaryProjectSuccess() {
            // 重新加载数据
            this.query();
        }
    }
};
</script>

<style lang="scss" scoped>
.project-management-list {
    padding: 20px;
}
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
