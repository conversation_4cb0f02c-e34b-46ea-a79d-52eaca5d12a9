<template>
    <div class="common-list-container">
        <!-- 搜索面板 -->
        <search-panel
            :value="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :table-columns="allColumns"
            :is-dot="hasSearchConditions"
            :default-open="defaultSearchOpen"
            :config-module="configModule"
            :config-section="configSection"
            :config-key="configKey"
            @input="handleNavTabChange"
            @search="handleSearch"
            @reset="handleReset"
            @navChange="handleNavChange"
            @columns-change="handleColumnsChange"
        >
            <!-- 透传搜索面板相关的插槽 -->
            <template v-for="slot in ['rightNav', 'leftNav']" #[slot]="scope">
                <slot :name="slot" v-bind="scope"></slot>
            </template>
        </search-panel>

        <!-- 表格列表 -->
        <base-list
            :key="columnsUpdateKey"
            ref="baseList"
            class="base-list"
            :columns="visibleColumns"
            :data="tableData"
            :actions-width="actionsWidth"
            :actions-fixed="actionsFixed"
            :rowKey="rowKey"
            :selectable="selectable"
            v-bind="tableAttrs"
            v-on="$listeners"
            @selection-change="handleSelectionChange"
        >
            <!-- 透传所有插槽 -->
            <template v-for="slot in Object.keys($scopedSlots)" #[slot]="scope">
                <slot :name="slot" v-bind="scope"></slot>
            </template>
        </base-list>
        <pagination class="pagination" :total="total" :page="page" :limit="limit" @pagination="handlePagination" />
    </div>
</template>

<script>
import SearchPanel from './components/SearchPanel.vue';
import BaseList from './components/BaseList.vue';

export default {
    name: 'CommonList',
    components: {
        SearchPanel,
        BaseList
    },
    props: {
        // v-model 绑定的值（当前激活的导航标签）
        value: {
            type: String,
            default: ''
        },
        // 导航栏配置
        navItems: {
            type: Array,
            default: () => []
        },
        // 查询参数
        queryParams: {
            type: Object,
            default: () => ({})
        },
        // 查询表单配置
        queryConfig: {
            type: Object,
            default: () => ({
                elFormAttrs: {},
                items: []
            })
        },
        // 表格列配置（包含所有可能的列）
        columns: {
            type: Array,
            required: true
        },
        // 表格数据
        data: {
            type: Array,
            default: () => []
        },
        // 操作列宽度
        actionsWidth: {
            type: [String, Number],
            default: 150
        },
        // 操作列是否固定
        actionsFixed: {
            type: [String, Boolean],
            default: 'right'
        },
        // 默认搜索面板是否打开
        defaultSearchOpen: {
            type: Boolean,
            default: false
        },
        // 表格其他属性
        tableAttrs: {
            type: Object,
            default: () => ({})
        },
        // 分页配置
        total: {
            type: Number,
            default: 0
        },
        page: {
            type: Number,
            default: 1
        },
        limit: {
            type: Number,
            default: 10
        },
        // 行数据的key
        rowKey: {
            type: [String, Function],
            default: 'id'
        },
        // 自定义行是否可选择的函数
        selectable: {
            type: Function,
            default: null
        },
        // 列配置存储参数
        configModule: {
            type: String,
            default: 'datatable'
        },
        configSection: {
            type: String,
            default: 'commonBrowse'
        },
        configKey: {
            type: String,
            default: 'cols'
        }
    },
    data() {
        return {
            tableData: [],
            allColumns: [],
            currentSearchParams: {},
            columnsUpdateKey: 0
        };
    },
    computed: {
        // 当前激活的导航标签（支持 v-model）
        activeNavTab: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        },
        // 可见的列（根据列管理配置过滤）
        visibleColumns() {
            return this.allColumns
                .filter((column) => column.columnManage?.show !== false)
                .map((column) => {
                    // 应用列管理的配置
                    const processedColumn = { ...column };

                    // 应用宽度设置
                    if (column?.width && column.columnManage.widthType !== 'auto') {
                        processedColumn.width = column.width;
                    }

                    // 应用排序设置
                    if (column.columnManage?.sortableDisabled) {
                        processedColumn.sortable = false;
                    }

                    return processedColumn;
                });
        },
        // 是否有搜索条件（用于显示红点）
        hasSearchConditions() {
            return false;
        }
    },
    watch: {
        // 监听 value 变化，确保初始化正确
        value: {
            handler(newValue) {
                // 如果传入的值为空且有导航项，设置为第一个导航项
                if (!newValue && this.navItems.length > 0) {
                    this.$emit('input', this.navItems[0].name);
                }
            },
            immediate: true
        },
        // 监听导航项变化，确保默认值正确
        navItems: {
            handler(newNavItems) {
                // 如果当前值为空且有导航项，设置为第一个导航项
                if (!this.value && newNavItems.length > 0) {
                    this.$emit('input', newNavItems[0].name);
                }
            },
            immediate: true
        },
        // 监听传入的列配置变化
        columns: {
            handler(newColumns) {
                this.initializeColumns(newColumns);
            },
            immediate: true,
            deep: true
        },
        // 监听传入的数据变化
        data: {
            handler(newData) {
                this.tableData = [...newData];
            },
            immediate: true,
            deep: true
        },
        // 监听查询参数变化
        queryParams: {
            handler(newParams) {
                this.currentSearchParams = { ...newParams };
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 初始化列配置
        initializeColumns(columns) {
            this.allColumns = columns.map((column) => {
                let processedColumn = { ...column };

                // 初始化列管理配置
                if (!processedColumn.columnManage) {
                    processedColumn.columnManage = {};
                }

                const defaultConfig = {
                    show: column.show !== undefined ? column.show : true,
                    visible: column.visible || false,
                    fixed: column.fixed || false,
                    sortableDisabled: column.sortableDisabled || false,
                    widthDisabled: column.widthDisabled || false,
                    widthType: column.width ? 'fixed' : 'auto',
                    width: column.width
                };

                processedColumn.columnManage = {
                    ...defaultConfig,
                    ...processedColumn.columnManage
                };
                // 初始化列渲染
                processedColumn = {
                    render: column.render || '',
                    ...processedColumn
                };
                return processedColumn;
            });
        },

        // 处理搜索
        handleSearch() {
            this.currentSearchParams = { ...this.queryParams };
            this.$emit('search', {
                searchParams: this.currentSearchParams,
                navTab: this.activeNavTab
            });
        },

        // 处理重置
        handleReset() {
            // 清空查询参数
            Object.keys(this.queryParams).forEach((key) => {
                this.$set(this.queryParams, key, '');
            });
            this.currentSearchParams = {};
            this.$emit('reset');
        },

        // 处理导航标签变化（支持 v-model）
        handleNavTabChange(value) {
            this.activeNavTab = value;
        },

        // 处理导航切换
        handleNavChange() {
            this.$emit('nav-change', this.activeNavTab);
        },

        // 处理列配置变化
        handleColumnsChange(columns) {
            this.initializeColumns(columns);
            // 强制更新计算属性
            this.columnsUpdateKey += 1;
            this.$emit('columns-change', columns);
        },

        // 获取表格实例
        getTableRef() {
            return this.$refs.baseList?.getTableRef();
        },

        // 清除选择
        clearSelection() {
            this.$refs.baseList.clearSelection();
        },

        // 获取选中的行
        getSelection() {
            return this.$refs.baseList.getSelection();
        },

        // 设置行选中状态
        toggleRowSelection(row, selected) {
            this.$refs.baseList.toggleRowSelection(row, selected);
        },

        // 获取当前搜索参数
        getCurrentSearchParams() {
            return {
                searchParams: this.currentSearchParams,
                navTab: this.activeNavTab
            };
        },

        // 获取当前列配置
        getCurrentColumns() {
            return this.allColumns;
        },

        // 选择项变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection);
        },

        // 处理分页变化
        handlePagination() {
            this.$emit('pagination', {
                page: this.page,
                limit: this.limit
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.common-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .search-panel {
        flex-shrink: 0;
    }

    .base-list {
        flex: 1;
        overflow: hidden;
    }
}
.base-list {
    margin-top: 15px;
}
.pagination.pagination-container {
    margin-top: 0px;
    padding-top: 10px;
    padding-bottom: 0;
}
</style>
